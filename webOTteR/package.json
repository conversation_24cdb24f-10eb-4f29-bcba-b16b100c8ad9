{"name": "webotr", "version": "0.1.0", "description": "Off-The-Record messaging for web chat platforms", "main": "src/index.js", "scripts": {"build": "webpack --mode production", "dev": "webpack --mode development --watch", "start": "node scripts/setup.js && npm run dev", "verify": "node scripts/verify-start.js", "test": "jest", "test:integration": "jest --config jest.integration.config.js", "test:e2e": "jest --config jest.e2e.config.js", "lint": "eslint src/**/*.js"}, "keywords": ["otr", "encryption", "privacy", "messaging", "teams", "discord", "slack"], "author": "", "license": "MIT", "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.26.9", "babel-jest": "^29.7.0", "babel-loader": "^9.1.2", "copy-webpack-plugin": "^11.0.0", "eslint": "^8.43.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.7.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.4"}, "dependencies": {"crypto-js": "^4.1.1", "jsbn": "^1.1.0"}}