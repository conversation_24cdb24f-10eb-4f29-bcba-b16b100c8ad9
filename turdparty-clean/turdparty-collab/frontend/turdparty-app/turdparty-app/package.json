{"name": "turdparty-app", "version": "1.0.0", "description": "TurdParty Application", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "eject": "react-scripts eject", "translate": "node scripts/translate.js"}, "dependencies": {"@ant-design/icons": "^5.2.6", "ajv": "^8.12.0", "antd": "^5.9.0", "axios": "^1.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.16.0", "react-scripts": "5.0.1", "i18next": "^23.5.1", "react-i18next": "^13.2.2", "i18next-http-backend": "^2.2.2", "i18next-browser-languagedetector": "^7.1.0"}, "devDependencies": {"@playwright/test": "^1.38.0", "@types/node": "^20.6.0", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "typescript": "^4.9.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}