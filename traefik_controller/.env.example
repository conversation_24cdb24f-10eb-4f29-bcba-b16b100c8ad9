# Traefik Controller Environment Configuration
# Copy this file to .env and customize as needed

# Domain configuration
DOMAIN_SUFFIX=10baht
TRAEFIK_DOMAIN=traefik.10baht

# Traefik configuration
TRAEFIK_LOG_LEVEL=INFO
TRAEFIK_API_INSECURE=false

# SSL/TLS configuration
SSL_CERT_PATH=./certs/10baht.crt
SSL_KEY_PATH=./certs/10baht.key

# Network configuration
TRAEFIK_NETWORK=traefik-network
HTTP_PORT=80
HTTPS_PORT=443
DASHBOARD_PORT=8080

# Project discovery
PROJECTS_BASE_DIR=../
PROJECTS_CONFIG_DIR=./projects

# Logging
ACCESS_LOG_ENABLED=true
ACCESS_LOG_PATH=/var/log/traefik/access.log
TRAEFIK_LOG_PATH=/var/log/traefik/traefik.log

# Metrics
PROMETHEUS_METRICS=true

# Development settings
DEV_MODE=true
AUTO_DISCOVER_PROJECTS=true
