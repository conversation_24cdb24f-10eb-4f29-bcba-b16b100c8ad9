# Traefik Controller Makefile
.PHONY: help setup start stop status discover start-all stop-all clean logs dashboard

# Default target
help:
	@echo "🚀 Traefik Controller Management"
	@echo "================================"
	@echo ""
	@echo "Available targets:"
	@echo "  setup       - Initial setup (install mkcert, start Traefik)"
	@echo "  start       - Start Traefik Controller"
	@echo "  stop        - Stop Traefik Controller"
	@echo "  status      - Show status of Traefik and projects"
	@echo "  discover    - Discover and configure projects"
	@echo "  start-all   - Start all projects"
	@echo "  stop-all    - Stop all projects"
	@echo "  clean       - Clean up containers and volumes"
	@echo "  logs        - Show Traefik logs"
	@echo "  dashboard   - Open Traefik dashboard"
	@echo ""
	@echo "Project-specific targets:"
	@echo "  start-PROJECT   - Start specific project (e.g., make start-bahtbrowse)"
	@echo "  stop-PROJECT    - Stop specific project (e.g., make stop-bahtbrowse)"
	@echo "  logs-PROJECT    - Show logs for specific project"
	@echo ""
	@echo "Examples:"
	@echo "  make setup"
	@echo "  make start-bahtbrowse"
	@echo "  make logs-certrats"

# Initial setup
setup:
	@echo "🔧 Setting up Traefik Controller..."
	./scripts/install-mkcert.sh
	./scripts/start.sh
	./scripts/discover-projects.sh

# Start Traefik Controller
start:
	./scripts/start.sh

# Stop Traefik Controller
stop:
	./scripts/stop.sh

# Show status
status:
	./scripts/status.sh

# Discover projects
discover:
	./scripts/discover-projects.sh

# Start all projects
start-all:
	./scripts/start-all-projects.sh

# Stop all projects
stop-all:
	./scripts/stop-all-projects.sh

# Clean up
clean:
	@echo "🧹 Cleaning up containers and volumes..."
	docker compose down -v
	docker system prune -f

# Show logs
logs:
	docker compose logs -f traefik

# Open dashboard
dashboard:
	@echo "🌐 Opening Traefik dashboard..."
	@if command -v xdg-open >/dev/null 2>&1; then \
		xdg-open "https://traefik.10baht"; \
	elif command -v open >/dev/null 2>&1; then \
		open "https://traefik.10baht"; \
	else \
		echo "🌐 Dashboard available at: https://traefik.10baht"; \
	fi

# Dynamic project targets
start-%:
	./scripts/start-project.sh $*

stop-%:
	./scripts/stop-project.sh $*

logs-%:
	@if [ -d "projects/$*" ]; then \
		cd projects/$* && docker compose -f docker-compose.traefik.yml logs -f; \
	else \
		echo "❌ Project '$*' not found"; \
	fi

# Development targets
dev-setup: setup
	@echo "🔧 Development setup complete"
	@echo "💡 Add this to your shell profile for easier access:"
	@echo "    alias traefik-ctl='$(PWD)/traefik-ctl'"

# Backup configuration
backup:
	@echo "💾 Creating backup..."
	tar -czf "traefik-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz" \
		--exclude='logs' \
		--exclude='certs' \
		--exclude='projects/*/docker-compose.traefik.yml' \
		.

# Restore from backup
restore:
	@echo "⚠️  This will restore configuration from backup"
	@echo "Make sure to specify the backup file: make restore BACKUP=filename.tar.gz"
	@if [ -z "$(BACKUP)" ]; then \
		echo "❌ Please specify backup file: make restore BACKUP=filename.tar.gz"; \
		exit 1; \
	fi
	tar -xzf $(BACKUP)

# Update Traefik image
update:
	@echo "🔄 Updating Traefik image..."
	docker compose pull traefik
	docker compose up -d traefik
