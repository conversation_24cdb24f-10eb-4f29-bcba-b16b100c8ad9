#!/bin/bash

# Setup dnsmasq for .10baht domains (NixOS compatible)
set -e

echo "🌐 Setting up dnsmasq for .10baht domains..."

# Check if dnsmasq is available
if ! command -v dnsmasq &> /dev/null; then
    echo "❌ dnsmasq not found. Install it with:"
    echo "   nix-shell -p dnsmasq"
    echo "   # or add to your NixOS configuration"
    exit 1
fi

# Create dnsmasq config for .10baht domains
mkdir -p ~/.config/dnsmasq

cat > ~/.config/dnsmasq/10baht.conf << EOF
# DNS configuration for .10baht domains
address=/.10baht/127.0.0.1

# Listen on localhost only
listen-address=127.0.0.1
port=5353

# Don't read /etc/hosts (since it's read-only)
no-hosts

# Forward other queries to system DNS
server=*******
server=*******

# Log queries for debugging
log-queries
log-facility=/tmp/dnsmasq.log
EOF

echo "✅ dnsmasq configuration created at ~/.config/dnsmasq/10baht.conf"
echo ""
echo "🚀 To start dnsmasq:"
echo "   dnsmasq --conf-file=~/.config/dnsmasq/10baht.conf --no-daemon"
echo ""
echo "🔧 Then configure your system to use 127.0.0.1:5353 as DNS server"
echo "💡 Or use it temporarily with:"
echo "   dig @127.0.0.1 -p 5353 traefik.10baht"
EOF
