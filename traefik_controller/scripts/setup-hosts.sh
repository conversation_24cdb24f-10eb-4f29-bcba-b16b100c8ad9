#!/bin/bash

# Setup /etc/hosts entries for .10baht domains
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"

echo "🌐 Setting up /etc/hosts for .10baht domains..."

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  This script needs sudo access to modify /etc/hosts"
    echo "🔧 Re-running with sudo..."
    sudo "$0" "$@"
    exit $?
fi

# Backup original hosts file
if [ ! -f /etc/hosts.backup ]; then
    echo "💾 Creating backup of /etc/hosts..."
    cp /etc/hosts /etc/hosts.backup
fi

# Remove existing .10baht entries
echo "🧹 Removing existing .10baht entries..."
sed -i '/\.10baht/d' /etc/hosts

# Add .10baht domains
echo "➕ Adding .10baht domains to /etc/hosts..."
cat >> /etc/hosts << EOF

# 10Baht Traefik Controller domains
127.0.0.1 traefik.10baht
127.0.0.1 turdparty.10baht
127.0.0.1 certrats.10baht
127.0.0.1 webotter.10baht
127.0.0.1 inspector-gadget.10baht
127.0.0.1 regression-rigor.10baht
127.0.0.1 hooker-pill.10baht
127.0.0.1 test-project.10baht

EOF

echo "✅ /etc/hosts updated successfully!"
echo ""
echo "🌐 The following domains now resolve to localhost:"
echo "   - traefik.10baht"
echo "   - turdparty.10baht"
echo "   - certrats.10baht"
echo "   - webotter.10baht"
echo "   - inspector-gadget.10baht"
echo "   - regression-rigor.10baht"
echo "   - hooker-pill.10baht"
echo "   - test-project.10baht"
echo ""
echo "💡 To add more domains later, edit /etc/hosts or re-run this script"
echo "🔄 To restore original hosts file: sudo cp /etc/hosts.backup /etc/hosts"
