#!/bin/bash

# Install mkcert for locally trusted SSL certificates
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"
CERTS_DIR="$TRAEFIK_DIR/certs"

echo "🔧 Installing mkcert for locally trusted SSL certificates..."

# Check if mkcert is already installed
if command -v mkcert &> /dev/null; then
    echo "✅ mkcert is already installed"
else
    echo "📦 Installing mkcert..."
    
    # Detect OS and install mkcert
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v apt-get &> /dev/null; then
            # Ubuntu/Debian
            sudo apt-get update
            sudo apt-get install -y libnss3-tools
            curl -JLO "https://dl.filippo.io/mkcert/latest?for=linux/amd64"
            chmod +x mkcert-v*-linux-amd64
            sudo mv mkcert-v*-linux-amd64 /usr/local/bin/mkcert
        elif command -v yum &> /dev/null; then
            # CentOS/RHEL/Fedora
            sudo yum install -y nss-tools
            curl -JLO "https://dl.filippo.io/mkcert/latest?for=linux/amd64"
            chmod +x mkcert-v*-linux-amd64
            sudo mv mkcert-v*-linux-amd64 /usr/local/bin/mkcert
        else
            echo "❌ Unsupported Linux distribution. Please install mkcert manually."
            exit 1
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install mkcert
        else
            echo "❌ Homebrew not found. Please install mkcert manually."
            exit 1
        fi
    else
        echo "❌ Unsupported operating system. Please install mkcert manually."
        exit 1
    fi
fi

# Create certificates directory
mkdir -p "$CERTS_DIR"

echo "🔑 Installing local CA..."
mkcert -install

echo "📜 Generating certificates for .10baht domains..."

# Generate wildcard certificate for .10baht domains
cd "$CERTS_DIR"
mkcert "*.10baht" "10baht" localhost 127.0.0.1 ::1

# Rename certificates to standard names
mv _wildcard.10baht+4.pem 10baht.crt
mv _wildcard.10baht+4-key.pem 10baht.key

echo "✅ SSL certificates generated successfully!"
echo "📁 Certificates saved to: $CERTS_DIR"
echo ""
echo "🌐 Your browser will now trust these domains:"
echo "   - *.10baht (all subdomains)"
echo "   - localhost"
echo "   - 127.0.0.1"
echo ""
echo "🚀 You can now start Traefik with: ./scripts/start.sh"
