#!/bin/bash

# Switch between simple (HTTP) and full (HTTPS) Traefik modes
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"

show_help() {
    echo "🔄 Switch Traefik Mode"
    echo "====================="
    echo ""
    echo "Switch between simple (HTTP only) and full (HTTPS) Traefik configurations"
    echo ""
    echo "Usage: $0 <mode>"
    echo ""
    echo "Modes:"
    echo "  simple    HTTP only, no SSL certificates needed"
    echo "  full      HTTPS with SSL certificates (default)"
    echo "  status    Show current mode"
    echo ""
    echo "Examples:"
    echo "  $0 simple    # Switch to simple HTTP-only mode"
    echo "  $0 full      # Switch to full HTTPS mode"
    echo "  $0 status    # Show current mode"
}

if [ $# -eq 0 ] || [ "$1" = "help" ] || [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_help
    exit 0
fi

cd "$TRAEFIK_DIR"

case "$1" in
    simple)
        echo "🔄 Switching to simple mode (HTTP only)..."
        
        # Stop current Traefik if running
        if docker ps | grep -q "traefik-controller"; then
            echo "🛑 Stopping current Traefik..."
            docker compose down
        fi
        
        # Backup current config and switch to simple
        if [ -f "docker-compose.yml" ]; then
            cp docker-compose.yml docker-compose.full.yml.backup
        fi
        cp docker-compose.simple.yml docker-compose.yml
        
        echo "✅ Switched to simple mode"
        echo "🚀 Start with: ./traefik-ctl start"
        echo "💡 Generate simple labels with: ./traefik-ctl add-labels myproject app 8000 --simple"
        echo "🌐 Dashboard will be at: http://localhost:8080"
        ;;
        
    full)
        echo "🔄 Switching to full mode (HTTPS)..."
        
        # Stop current Traefik if running
        if docker ps | grep -q "traefik-controller"; then
            echo "🛑 Stopping current Traefik..."
            docker compose down
        fi
        
        # Restore full config
        if [ -f "docker-compose.full.yml.backup" ]; then
            cp docker-compose.full.yml.backup docker-compose.yml
        else
            echo "⚠️  No backup found, using default full configuration"
            # Here you could regenerate the full config or use a template
        fi
        
        echo "✅ Switched to full mode"
        echo "🔑 Make sure SSL certificates are installed: ./scripts/install-mkcert.sh"
        echo "🚀 Start with: ./traefik-ctl start"
        echo "💡 Generate HTTPS labels with: ./traefik-ctl add-labels myproject app 8000"
        echo "🌐 Dashboard will be at: https://traefik.10baht"
        ;;
        
    status)
        echo "📊 Current Traefik Mode"
        echo "======================"
        
        if [ -f "docker-compose.yml" ]; then
            if grep -q "443:443" docker-compose.yml; then
                echo "🔒 Mode: FULL (HTTPS with SSL)"
                echo "🌐 Dashboard: https://traefik.10baht"
                echo "📜 Certificates: Required"
            else
                echo "🌐 Mode: SIMPLE (HTTP only)"
                echo "🌐 Dashboard: http://localhost:8080"
                echo "📜 Certificates: Not needed"
            fi
            
            # Check if running
            if docker ps | grep -q "traefik-controller"; then
                echo "🟢 Status: RUNNING"
            else
                echo "🔴 Status: STOPPED"
            fi
        else
            echo "❌ No docker-compose.yml found"
        fi
        ;;
        
    *)
        echo "❌ Unknown mode: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
