#!/bin/bash

# Stop Traefik Controller
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"

echo "🛑 Stopping Traefik Controller..."

# Change to traefik directory
cd "$TRAEFIK_DIR"

# Stop Traefik
docker compose down

echo "✅ Traefik Controller stopped successfully!"
echo ""
echo "💡 To stop all projects as well, run:"
echo "   ./scripts/stop-all-projects.sh"
