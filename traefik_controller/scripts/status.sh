#!/bin/bash

# Show status of Traefik Controller and all projects
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"
PROJECTS_DIR="$TRAEFIK_DIR/projects"

echo "📊 Traefik Controller Status"
echo "================================"

# Check Traefik status
cd "$TRAEFIK_DIR"
if docker ps | grep -q "traefik-controller"; then
    echo "✅ Traefik Controller: RUNNING"
    echo "🌐 Dashboard: https://traefik.10baht"
    echo "📊 Metrics: http://localhost:8080/metrics"
else
    echo "❌ Traefik Controller: STOPPED"
    echo "🚀 Start with: ./scripts/start.sh"
fi

echo ""
echo "📦 Project Status"
echo "================================"

# Check if projects directory exists
if [ ! -d "$PROJECTS_DIR" ]; then
    echo "❌ No projects configured"
    echo "🔧 Run './scripts/discover-projects.sh' to configure projects"
    exit 0
fi

# Find all project directories
PROJECTS=()
for project_dir in "$PROJECTS_DIR"/*; do
    if [ -d "$project_dir" ]; then
        project_name=$(basename "$project_dir")
        PROJECTS+=("$project_name")
    fi
done

if [ ${#PROJECTS[@]} -eq 0 ]; then
    echo "❌ No projects found"
    echo "🔧 Run './scripts/discover-projects.sh' to configure projects"
    exit 0
fi

RUNNING_COUNT=0
STOPPED_COUNT=0

for project in "${PROJECTS[@]}"; do
    project_config_dir="$PROJECTS_DIR/$project"
    
    # Get domain from project info
    domain="$project.10baht"
    if [ -f "$project_config_dir/project-info.yml" ]; then
        domain=$(grep "domain:" "$project_config_dir/project-info.yml" | cut -d' ' -f2)
    fi
    
    # Check if project is running
    cd "$project_config_dir"
    if [ -f "docker-compose.traefik.yml" ] && docker compose -f docker-compose.traefik.yml ps | grep -q "Up"; then
        echo "✅ $project -> https://$domain"
        ((RUNNING_COUNT++))
    else
        echo "❌ $project -> https://$domain (stopped)"
        ((STOPPED_COUNT++))
    fi
done

echo ""
echo "📊 Summary"
echo "================================"
echo "📦 Total projects: ${#PROJECTS[@]}"
echo "✅ Running: $RUNNING_COUNT"
echo "❌ Stopped: $STOPPED_COUNT"

if [ $STOPPED_COUNT -gt 0 ]; then
    echo ""
    echo "🚀 Start projects with:"
    echo "   ./scripts/start-project.sh <project-name>"
    echo "   ./scripts/start-all-projects.sh"
fi

echo ""
echo "🔧 Management commands:"
echo "   ./scripts/discover-projects.sh  - Refresh project configurations"
echo "   ./scripts/start-all-projects.sh - Start all projects"
echo "   ./scripts/stop-all-projects.sh  - Stop all projects"
