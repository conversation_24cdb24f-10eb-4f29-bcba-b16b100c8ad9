# Example: Adding Traefik labels to your project's docker-compose.yml
# Copy the relevant parts to your project's .dockerwrapper/docker-compose.yml

version: '3.8'

services:
  # Example: Web application (API backend)
  api:
    build: .
    container_name: myproject_api
    ports:
      - "8000"  # Don't expose ports externally - Traefik will handle routing
    networks:
      - default
      - traefik-network  # Connect to Traefik network
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"
      
      # HTTPS routing
      - "traefik.http.routers.myproject-api.rule=Host(`myproject.10baht`) && PathPrefix(`/api`)"
      - "traefik.http.routers.myproject-api.entrypoints=websecure"
      - "traefik.http.routers.myproject-api.tls=true"
      - "traefik.http.services.myproject-api.loadbalancer.server.port=8000"
      - "traefik.http.routers.myproject-api.priority=10"  # Higher priority for API routes
      
      # HTTP to HTTPS redirect
      - "traefik.http.routers.myproject-api-http.rule=Host(`myproject.10baht`) && PathPrefix(`/api`)"
      - "traefik.http.routers.myproject-api-http.entrypoints=web"
      - "traefik.http.routers.myproject-api-http.middlewares=redirect-to-https"

  # Example: Frontend application (React/Vue/etc)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: myproject_frontend
    ports:
      - "3000"  # Don't expose ports externally
    networks:
      - default
      - traefik-network
    labels:
      - "traefik.enable=true"
      
      # HTTPS routing (catch-all for frontend)
      - "traefik.http.routers.myproject-frontend.rule=Host(`myproject.10baht`)"
      - "traefik.http.routers.myproject-frontend.entrypoints=websecure"
      - "traefik.http.routers.myproject-frontend.tls=true"
      - "traefik.http.services.myproject-frontend.loadbalancer.server.port=3000"
      - "traefik.http.routers.myproject-frontend.priority=1"  # Lower priority (catch-all)
      
      # HTTP to HTTPS redirect
      - "traefik.http.routers.myproject-frontend-http.rule=Host(`myproject.10baht`)"
      - "traefik.http.routers.myproject-frontend-http.entrypoints=web"
      - "traefik.http.routers.myproject-frontend-http.middlewares=redirect-to-https"

  # Example: Database (no Traefik labels needed)
  db:
    image: postgres:14-alpine
    container_name: myproject_db
    environment:
      POSTGRES_DB: myproject
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - db_data:/var/lib/postgresql/data
    networks:
      - default  # Only on internal network
    # No Traefik labels - database should not be exposed externally

networks:
  # Connect to the external Traefik network
  traefik-network:
    external: true
    name: traefik-network
  
  # Internal network for services to communicate
  default:
    driver: bridge

volumes:
  db_data:

# Common label patterns:
#
# Basic service:
# - "traefik.enable=true"
# - "traefik.http.routers.SERVICE_NAME.rule=Host(`domain.10baht`)"
# - "traefik.http.routers.SERVICE_NAME.entrypoints=websecure"
# - "traefik.http.routers.SERVICE_NAME.tls=true"
# - "traefik.http.services.SERVICE_NAME.loadbalancer.server.port=PORT"
#
# Path-based routing:
# - "traefik.http.routers.SERVICE_NAME.rule=Host(`domain.10baht`) && PathPrefix(`/path`)"
#
# Multiple domains:
# - "traefik.http.routers.SERVICE_NAME.rule=Host(`domain1.10baht`) || Host(`domain2.10baht`)"
#
# Priority (higher number = higher priority):
# - "traefik.http.routers.SERVICE_NAME.priority=10"
#
# Custom middleware:
# - "traefik.http.routers.SERVICE_NAME.middlewares=middleware-name"
