# Example test project showing Trae<PERSON><PERSON> self-registration
# This would go in your project's .dockerwrapper/docker-compose.yml

version: '3.8'

services:
  web:
    image: nginx:alpine
    container_name: test_project_web
    volumes:
      - ./index.html:/usr/share/nginx/html/index.html:ro
    networks:
      - default
      - traefik-network
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"
      
      # HTTPS routing
      - "traefik.http.routers.test-project.rule=Host(`test-project.10baht`)"
      - "traefik.http.routers.test-project.entrypoints=websecure"
      - "traefik.http.routers.test-project.tls=true"
      - "traefik.http.services.test-project.loadbalancer.server.port=80"
      
      # HTTP to HTTPS redirect
      - "traefik.http.routers.test-project-http.rule=Host(`test-project.10baht`)"
      - "traefik.http.routers.test-project-http.entrypoints=web"
      - "traefik.http.routers.test-project-http.middlewares=redirect-to-https"

networks:
  traefik-network:
    external: true
    name: traefik-network
