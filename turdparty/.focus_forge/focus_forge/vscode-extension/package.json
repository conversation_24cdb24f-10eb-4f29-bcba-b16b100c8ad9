{"name": "focus-forge", "displayName": "Focus Forge", "description": "Task management sidebar for displaying tasks relevant to your current file", "version": "0.1.0", "publisher": "focus-forge", "engines": {"vscode": "^1.80.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "focus-forge.refreshTasks", "title": "Refresh Tasks", "category": "Focus Forge"}, {"command": "focus-forge.createTask", "title": "Create Task from Selection", "category": "Focus Forge"}, {"command": "focus-forge.linkTaskToCode", "title": "Link Task to Current Position", "category": "Focus Forge"}, {"command": "focus-forge.setTaskStatus", "title": "Update Task Status", "category": "Focus Forge"}], "viewsContainers": {"activitybar": [{"id": "focus-forge-sidebar", "title": "Focus Forge", "icon": "resources/focus-forge.svg"}]}, "views": {"focus-forge-sidebar": [{"id": "focus-forge-current-file", "name": "Current File Tasks"}, {"id": "focus-forge-module-tasks", "name": "Module Tasks"}, {"id": "focus-forge-related-tasks", "name": "Related Tasks"}]}, "configuration": {"title": "Focus Forge", "properties": {"focus-forge.tasksFilePath": {"type": "string", "default": "tasks.json", "description": "Path to the tasks.json file relative to the workspace root"}, "focus-forge.relevanceThreshold": {"type": "number", "default": 0.3, "description": "Minimum relevance score (0-1) for tasks to be shown in the current file view"}, "focus-forge.showCompletedTasks": {"type": "boolean", "default": false, "description": "Show completed tasks in the sidebar"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "lint": "eslint . --ext .ts,.tsx", "pretest": "npm run compile && npm run lint", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.1", "@types/node": "^16.18.34", "@types/vscode": "^1.80.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.26.0", "glob": "^8.1.0", "mocha": "^10.2.0", "typescript": "^5.1.3", "@vscode/test-electron": "^2.3.8"}}