{"name": "regression-rigor-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "axios": "^1.4.0", "chart.js": "^4.3.0", "jwt-decode": "^3.1.2", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.1", "@types/node": "^20.1.4", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "ts-jest": "^29.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/index.tsx", "!src/reportWebVitals.ts", "!src/serviceWorker.ts"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}}