{"name": "swiss-budget-pro", "version": "1.0.0", "description": "Advanced Swiss financial planning and retirement calculator", "type": "module", "scripts": {"dev": "vite", "build": "npm run type-check && vite build", "build:skip-types": "vite build", "preview": "vite preview --host 0.0.0.0 --port 4173", "start": "npm run preview", "test": "vitest", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "validate": "npm run lint && npm run type-check && npm run format:check", "pre-commit": "npm run validate && npm run test:run", "ci": "node scripts/local-ci.js run", "ci:watch": "node scripts/local-ci.js watch", "ci:quick": "node scripts/local-ci.js quick", "detect-issues": "node scripts/detect-critical-issues.js", "setup-hooks": "node scripts/setup-git-hooks.js", "health-check": "npm run ci:quick && npm run detect-issues", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:e2e:install": "playwright install", "test:e2e:runner": "ts-node tests/e2e/scripts/run-tests.ts", "test:e2e:smoke": "npm run test:e2e:runner -- --suite smoke", "test:e2e:critical": "npm run test:e2e:runner -- --suite critical", "test:e2e:regression": "npm run test:e2e:runner -- --suite regression", "test:e2e:full": "npm run test:e2e:runner -- --suite full", "test:e2e:mobile": "npm run test:e2e:runner -- --suite ui --browsers chromium", "test:e2e:performance": "npm run test:e2e:runner -- --suite performance", "test:e2e:accessibility": "playwright test tests/e2e/tests/accessibility/", "test:e2e:visual": "playwright test tests/e2e/tests/visual/", "test:e2e:forms": "playwright test tests/e2e/tests/form-validation/", "test:e2e:data": "playwright test tests/e2e/tests/data-management/", "test:e2e:errors": "playwright test tests/e2e/tests/error-handling/", "test:e2e:healthcare": "playwright test tests/e2e/tests/swiss-features/healthcare-cost-optimizer.spec.ts", "test:e2e:healthcare-performance": "playwright test tests/e2e/tests/performance/healthcare-performance.spec.ts", "test:e2e:healthcare-accessibility": "playwright test tests/e2e/tests/accessibility/healthcare-accessibility.spec.ts", "test:e2e:healthcare-journeys": "playwright test tests/e2e/tests/user-journeys/healthcare-user-journeys.spec.ts", "test:e2e:healthcare-all": "npm run test:e2e:healthcare && npm run test:e2e:healthcare-performance && npm run test:e2e:healthcare-accessibility && npm run test:e2e:healthcare-journeys", "test:e2e:journeys": "playwright test tests/e2e/tests/user-journeys/", "test:e2e:dry-run": "npm run test:e2e:runner -- --dry-run", "test:e2e:swiss": "node scripts/run-e2e-tests.js critical chromium", "test:e2e:swiss-mobile": "node scripts/run-e2e-tests.js mobile mobile-chrome", "test:e2e:swiss-all": "node scripts/run-e2e-tests.js all chromium", "test:bdd": "cd tests/bdd && python run_bdd_tests.py critical", "test:bdd:fire": "cd tests/bdd && python run_bdd_tests.py fire", "test:bdd:tax": "cd tests/bdd && python run_bdd_tests.py tax", "test:bdd:healthcare": "cd tests/bdd && python run_bdd_tests.py healthcare", "test:bdd:mobile": "cd tests/bdd && python run_bdd_tests.py mobile", "test:bdd:accessibility": "cd tests/bdd && python run_bdd_tests.py accessibility", "test:bdd:swiss": "cd tests/bdd && python run_bdd_tests.py swiss", "test:bdd:smoke": "cd tests/bdd && python run_bdd_tests.py --tags @smoke", "test:bdd:regression": "cd tests/bdd && python run_bdd_tests.py --tags @regression", "test:bdd:all": "cd tests/bdd && python run_bdd_tests.py all", "test:bdd:headless": "cd tests/bdd && python run_bdd_tests.py critical --headless", "test:bdd:parallel": "cd tests/bdd && python run_bdd_tests.py critical --parallel", "test:bdd:setup": "cd tests/bdd && pip install -r requirements.txt && playwright install", "test:all": "npm run test:run && npm run test:e2e:smoke && npm run test:bdd", "test:comprehensive": "npm run test:run && npm run test:e2e:swiss-all && npm run test:bdd:all", "docker:dev": "./fire.sh dev", "docker:dev:logs": "./fire.sh dev logs", "docker:dev:shell": "./fire.sh dev shell", "docker:dev:stop": "./fire.sh dev stop", "docker:prod": "./fire.sh prod", "docker:prod:logs": "./fire.sh prod logs", "docker:prod:shell": "./fire.sh prod shell", "docker:prod:deploy": "./fire.sh prod deploy", "docker:prod:stop": "./fire.sh prod stop"}, "dependencies": {"@tailwindcss/postcss": "^4.1.7", "autoprefixer": "^10.4.21", "d3": "^7.9.0", "decimal.js": "^10.5.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "postcss": "^8.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "react-router-dom": "^6.20.0", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/js": "^9.15.0", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/d3": "^7.4.3", "@types/decimal.js": "^0.0.32", "@types/node": "^20.10.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-i18next": "^7.8.3", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^3.2.1", "@vitest/ui": "^3.1.4", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "jsdom": "^23.2.0", "prettier": "^3.3.3", "ts-node": "^10.9.1", "typescript": "^5.2.2", "typescript-eslint": "^8.15.0", "vite": "^6.3.5", "vitest": "^3.1.4"}, "keywords": ["react", "typescript", "financial-planning", "retirement-calculator", "swiss-finance", "budgeting"], "author": "Swiss Budget Pro Team", "license": "MIT"}