{"name": "focus-forge", "displayName": "Focus Forge", "description": "Task management sidebar for displaying tasks relevant to your current file", "version": "0.1.0", "publisher": "focus-forge", "private": true, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/forkrul/focus_forge"}, "engines": {"vscode": "^1.80.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "focus-forge.refreshTasks", "title": "Refresh Tasks", "category": "Focus Forge"}, {"command": "focus-forge.createTask", "title": "Create Task from Selection", "category": "Focus Forge"}, {"command": "focus-forge.linkTaskToCode", "title": "Link Task to Current Position", "category": "Focus Forge"}, {"command": "focus-forge.setTaskStatus", "title": "Update Task Status", "category": "Focus Forge"}], "viewsContainers": {"activitybar": [{"id": "focus-forge", "title": "Focus Forge", "icon": "resources/focus-forge.svg"}]}, "views": {"focus-forge": [{"id": "currentFileTasks", "name": "Current File Tasks"}, {"id": "moduleTasks", "name": "Module Tasks"}, {"id": "relatedTasks", "name": "Related Tasks"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "lint": "eslint . --ext .ts,.tsx", "pretest": "npm run compile && npm run lint", "test": "node ./out/test/runTest.js", "package": "npx @vscode/vsce package"}, "devDependencies": {"@types/glob": "^7.2.0", "@types/mocha": "^10.0.1", "@types/node": "^16.18.34", "@types/vscode": "^1.80.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vscode/test-electron": "^2.3.8", "@vscode/vsce": "^3.3.2", "eslint": "^8.26.0", "glob": "^7.2.3", "mocha": "^10.2.0", "typescript": "^5.1.3"}}